import os

from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver


def upload_avatar(instance, filename):
    return 'avatar/{}/{}'.format(instance.user.username, filename)


class Profile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    # common information
    avatar = models.ImageField(
        upload_to=upload_avatar, default='avatar/user.jpg')
    phone = models.CharField(max_length=50, default='')
    address = models.CharField(max_length=200, default='')
    ward = models.CharField(max_length=100, default='')
    district = models.CharField(max_length=100, default='')
    province = models.CharField(max_length=100, default='')
    about = models.TextField(default="")

    # required
    date_joined = models.DateTimeField(auto_now_add=True, editable=False)
    last_login = models.DateTimeField(auto_now=True, editable=False)

    # default
    # is_trainee = models.Bo<PERSON>anField(default=True)
    is_active = models.BooleanField(default=True)

    # For back-office
    is_admin = models.BooleanField(default=False)
    is_supervisor = models.BooleanField(default=False)
    is_staff = models.BooleanField(default=False)

    # For sale
    is_saler = models.BooleanField(default=False)

    # For academy
    is_teacher = models.BooleanField(default=False)
    is_student = models.BooleanField(default=False)

    # For reset password
    reset_id = models.CharField(max_length=200, default='', editable=False)
    valid_until = models.DateTimeField(default=None, null=True, editable=False)

    def name(self):
        return f'{self.user.first_name} {self.user.last_name}'

    def full_address(self):
        return f'{self.address}, {self.ward}, {self.district}, {self.province}'

    def save(self, *args, **kwargs):
        try:
            this = Profile.objects.get(id=self.id)
            if this.avatar != self.avatar and this.avatar.name != 'avatar/user.jpg':
                if os.path.isfile(this.avatar.path):
                    os.remove(this.avatar.path)
        except Profile.DoesNotExist:
            pass

        super(Profile, self).save(*args, **kwargs)

    def __str__(self):
        return self.user.email


@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        Profile.objects.create(user=instance)


@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    instance.profile.save()


class Membership(models.Model):
    """Model to control membership and point"""
    name = models.CharField(max_length=200)
    phone = models.CharField(max_length=20, unique=True)
    joined_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    points = models.IntegerField(default=0)  # Số điểm tích lũy
    seller = models.ForeignKey(User, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.name} - {self.phone}"

    def member(self):
        return f"{self.name} - {self.phone}"


class PointTransaction(models.Model):
    """Giao dịch tích điểm hoặc sử dụng điểm"""
    membership = models.ForeignKey(Membership, on_delete=models.CASCADE)
    points = models.IntegerField()  # Điểm (+ tích, - sử dụng)
    description = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.membership.name} ({self.points} điểm) - {self.description}"
