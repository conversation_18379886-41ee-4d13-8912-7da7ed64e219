import React, { useState, useEffect } from "react";
import AdminLayout from "../Common/AdminLayout2";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";
import Grid from "@mui/material/Grid2";
import {
    Box,
    Paper,
    Typography,
    Divider,
    Modal,
    IconButton,
    Autocomplete,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Chip,
    Button,
} from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import { deepPurple, grey, green, red, blue } from "@mui/material/colors";
import axiosInstance from "../../../services/axiosInstance";
import Loading from "../../Common/Loading";
import CustomTextField from "../../Common/CustomTextField";
import CustomButton from "../../Common/CustomButton";
import { CKEditor } from "ckeditor4-react";
import VisibilityIcon from "@mui/icons-material/Visibility";
// import ClassicEditor from "@ckeditor/ckeditor5-build-classic";

// Icons
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckIcon from "@mui/icons-material/Check";
import ClassIcon from "@mui/icons-material/Class";
import MenuBookIcon from "@mui/icons-material/MenuBook";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";

function LessonCard({ lesson, handleEdit, handleDelete, handleView }) {
    return (
        <Paper sx={{ p: 2, borderRadius: "15px", mb: 2, position: "relative" }}>
            <Box sx={{ position: "absolute", top: 10, right: 10 }}>
                <IconButton onClick={() => handleView(lesson.id)} size="small">
                    <VisibilityIcon sx={{ color: blue[600] }} />
                </IconButton>
                <IconButton onClick={() => handleEdit(lesson.id)} size="small">
                    <EditIcon sx={{ color: green[600] }} />
                </IconButton>
                <IconButton onClick={() => handleDelete(lesson.id)} size="small">
                    <DeleteIcon sx={{ color: red[600] }} />
                </IconButton>
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                <MenuBookIcon sx={{ mr: 1, color: blue[500] }} />
                <Typography variant="h6">{lesson.title}</Typography>
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                <ClassIcon sx={{ mr: 1, color: deepPurple[500], fontSize: "1rem" }} />
                <Typography variant="body2" color="text.secondary">
                    Lớp: {lesson.class_ref.name}
                </Typography>
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <CalendarMonthIcon sx={{ mr: 1, color: green[500], fontSize: "1rem" }} />
                <Typography variant="body2" color="text.secondary">
                    Ngày: {new Date(lesson.date).toLocaleDateString("vi-VN")}
                </Typography>
            </Box>

            <Divider sx={{ mb: 2 }} />

            <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: "bold" }}>
                Nội dung:
            </Typography>
            <Box
                sx={{
                    mb: 2,
                    maxHeight: "100px",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    position: "relative",
                }}
            >
                <div dangerouslySetInnerHTML={{ __html: lesson.content }} />
                <Box
                    sx={{
                        position: "absolute",
                        bottom: 0,
                        left: 0,
                        right: 0,
                        height: "50px",
                        background: "linear-gradient(transparent, white)",
                    }}
                />
            </Box>

            {lesson.result_summary && (
                <>
                    <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: "bold" }}>
                        Kết quả:
                    </Typography>
                    <Box
                        sx={{
                            maxHeight: "50px",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            position: "relative",
                        }}
                    >
                        <div dangerouslySetInnerHTML={{ __html: lesson.result_summary }} />
                        <Box
                            sx={{
                                position: "absolute",
                                bottom: 0,
                                left: 0,
                                right: 0,
                                height: "30px",
                                background: "linear-gradient(transparent, white)",
                            }}
                        />
                    </Box>
                </>
            )}
        </Paper>
    );
}

function LessonModal({ open, handleEditModalClose, isEditing, editingLesson, classes, setRequestRefreshLessons }) {
    const [editorReady, setEditorReady] = React.useState(false);
    const [loading, setLoading] = React.useState(true);

    const [lessonData, setLessonData] = useState({
        class_ref: null,
        title: "",
        date: dayjs(),
        content: "",
        result_summary: "",
    });

    React.useEffect(() => {
        if (!loading && editorReady && isEditing) {
            for (var i = 0; i < 2; i++) {
                const editorName = Object.keys(window.CKEDITOR.instances)[i];
                if (editorName)
                    window.CKEDITOR.instances[editorName].setData(
                        i === 0 ? lessonData.content : lessonData.result_summary
                    );
            }
        }
    }, [loading, editorReady]);

    useEffect(() => {
        if (isEditing && editingLesson) {
            setLessonData({
                class_ref: editingLesson.class_ref,
                title: editingLesson.title,
                date: dayjs(editingLesson.date),
                content: editingLesson.content,
                result_summary: editingLesson.result_summary || "",
            });
            // for (var i = 0; i < 2; i++) {
            //     const editorName = Object.keys(window.CKEDITOR.instances)[i];
            //     console.log(editorName);
            //     if (editorName)
            //         window.CKEDITOR.instances[editorName].setData(
            //             i === 0 ? editingLesson.content : editingLesson.result_summary
            //         );
            // }
        } else {
            setLessonData({
                class_ref: null,
                title: "",
                date: dayjs(),
                content: "",
                result_summary: "",
            });
            if (window.CKEDITOR.instances) {
                for (var i = 0; i < 2; i++) {
                    const editorName = Object.keys(window.CKEDITOR.instances)[i];
                    if (editorName) window.CKEDITOR.instances[editorName].setData("");
                }
            }
        }
        setLoading(false);
    }, [isEditing, editingLesson]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setLessonData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleDateChange = (date) => {
        setLessonData((prev) => ({
            ...prev,
            date: date,
        }));
    };

    const handleClassChange = (event, newValue) => {
        setLessonData((prev) => ({
            ...prev,
            class_ref: newValue,
        }));
    };

    const handleContentChange = (event) => {
        const data = event.editor.getData();
        setLessonData((prev) => ({
            ...prev,
            content: data,
        }));
    };

    const handleResultChange = (event) => {
        const data = event.editor.getData();
        setLessonData((prev) => ({
            ...prev,
            result_summary: data,
        }));
    };

    const handleSubmit = async () => {
        if (!lessonData.class_ref || !lessonData.title || !lessonData.date) {
            alert("Vui lòng điền đầy đủ thông tin bắt buộc");
            return;
        }

        const formData = {
            class_ref_id: lessonData.class_ref.id,
            title: lessonData.title,
            date: lessonData.date.format("YYYY-MM-DD"),
            content: lessonData.content,
            result_summary: lessonData.result_summary,
        };

        try {
            if (isEditing && editingLesson) {
                await axiosInstance.patch(`/api/academy/lesson/${editingLesson.id}`, formData);
            } else {
                await axiosInstance.post("/api/academy/lesson/", formData);
            }

            handleEditModalClose();
            setRequestRefreshLessons(true);
        } catch (error) {
            console.error("Error saving lesson:", error);
            alert("Có lỗi xảy ra khi lưu bài học");
        }
    };

    const style = {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: "80%",
        maxWidth: "1000px",
        maxHeight: "90vh",
        overflow: "auto",
        bgcolor: "background.paper",
        border: "none",
        boxShadow: 24,
        p: 4,
        borderRadius: "20px",
    };

    return (
        <Modal open={open} onClose={handleEditModalClose} sx={{ borderRadius: "20px", border: "none" }}>
            <Box sx={style}>
                <Typography id="modal-modal-title" variant="h6" component="h2" sx={{ mb: 2 }}>
                    {isEditing ? "Chỉnh sửa bài học" : "Tạo bài học mới"}
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                    <Grid size={{ xs: 12, md: 6 }}>
                        <Autocomplete
                            options={classes}
                            getOptionLabel={(option) => option.name}
                            value={lessonData.class_ref}
                            onChange={handleClassChange}
                            renderInput={(params) => (
                                <CustomTextField
                                    {...params}
                                    label="Lớp học"
                                    placeholder="Chọn lớp học"
                                    fullWidth
                                    required
                                />
                            )}
                            sx={{ mb: 2 }}
                        />
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                            <DatePicker
                                label="Ngày học"
                                value={lessonData.date}
                                onChange={handleDateChange}
                                slots={{
                                    textField: CustomTextField,
                                }}
                                slotProps={{
                                    textField: {
                                        fullWidth: true,
                                        required: true,
                                    },
                                }}
                                sx={{ mb: 2 }}
                            />
                        </LocalizationProvider>
                    </Grid>
                </Grid>

                <CustomTextField
                    label="Tiêu đề bài học"
                    name="title"
                    value={lessonData.title}
                    onChange={handleChange}
                    fullWidth
                    required
                    sx={{ mb: 2 }}
                />

                <Typography variant="subtitle1" sx={{ mb: 1 }}>
                    Nội dung bài học:
                </Typography>
                <Box sx={{ mb: 2 }}>
                    <CKEditor
                        initData={lessonData.content}
                        data={lessonData.content}
                        onChange={handleContentChange}
                        onInstanceReady={(evt) => {
                            setEditorReady(true);
                        }}
                        config={{
                            versionCheck: false,
                            extraPlugins: "clipboard,pastetools,uploadimage,tabletools,tableproperties",
                            clipboard_handleImages: true,
                            toolbar: [
                                {
                                    name: "paragraph",
                                    items: ["Format"],
                                },
                                {
                                    name: "basicstyles",
                                    items: ["Bold", "Italic", "Underline", "Strike"],
                                },
                                {
                                    name: "table",
                                    items: ["Table", "TableProperties", "TableBorder", "TableDelete"],
                                },
                                {
                                    name: "list",
                                    items: ["NumberedList", "BulletedList"],
                                },
                                {
                                    name: "align",
                                    items: ["JustifyLeft", "JustifyCenter", "JustifyRight", "JustifyBlock"],
                                },
                                {
                                    name: "links",
                                    items: ["Link", "Unlink"],
                                },
                                {
                                    name: "insert",
                                    items: ["Image"],
                                },
                                {
                                    name: "tools",
                                    items: ["Maximize"],
                                },
                            ],
                            format_tags: "p;h1;h2;h3;pre",
                            removeButtons: "",
                            height: 200,
                        }}
                        style={{
                            borderRadius: "10px",
                            overflow: "hidden",
                        }}
                    />
                </Box>

                <Typography variant="subtitle1" sx={{ mb: 1 }}>
                    Kết quả buổi học:
                </Typography>
                <Box sx={{ mb: 2 }}>
                    <CKEditor
                        initData={lessonData.result_summary}
                        data={lessonData.result_summary}
                        onChange={handleResultChange}
                        config={{
                            versionCheck: false,
                            extraPlugins: "clipboard,pastetools,uploadimage",
                            clipboard_handleImages: true,
                            toolbar: [
                                {
                                    name: "paragraph",
                                    items: ["Format"],
                                },
                                {
                                    name: "basicstyles",
                                    items: ["Bold", "Italic", "Underline", "Strike"],
                                },
                                {
                                    name: "list",
                                    items: ["NumberedList", "BulletedList"],
                                },
                                {
                                    name: "align",
                                    items: ["JustifyLeft", "JustifyCenter", "JustifyRight", "JustifyBlock"],
                                },
                                {
                                    name: "links",
                                    items: ["Link", "Unlink"],
                                },
                                {
                                    name: "insert",
                                    items: ["Image"],
                                },
                                {
                                    name: "tools",
                                    items: ["Maximize"],
                                },
                            ],
                            format_tags: "p;h1;h2;h3;pre",
                            removeButtons: "",
                            height: 200,
                        }}
                        style={{
                            borderRadius: "10px",
                            overflow: "hidden",
                        }}
                    />
                </Box>

                <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
                    <CustomButton variant="contained" onClick={handleSubmit} startIcon={<CheckIcon />}>
                        {isEditing ? "Cập nhật" : "Tạo bài học"}
                    </CustomButton>
                    <Button
                        variant="outlined"
                        color="inherit"
                        sx={{ borderRadius: "10px", textTransform: "none" }}
                        onClick={handleEditModalClose}
                    >
                        Hủy
                    </Button>
                </Box>
            </Box>
        </Modal>
    );
}

function LessonModalView({ open, handleEditModalClose, editingLesson, classes }) {
    const [lesson, setLesson] = useState(
        editingLesson
            ? editingLesson
            : {
                  title: "",
                  content: "",
                  result_summary: "",
              }
    );

    React.useEffect(() => {
        console.log(editingLesson);
        if (editingLesson) {
            setLesson(editingLesson);
        }
    }, [editingLesson]);

    const style = {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: "80%",
        maxWidth: "1000px",
        maxHeight: "90vh",
        overflow: "auto",
        bgcolor: "background.paper",
        border: "none",
        boxShadow: 24,
        p: 4,
        borderRadius: "20px",
    };

    return (
        <Modal open={open} onClose={handleEditModalClose} sx={{ borderRadius: "20px", border: "none" }}>
            <Box sx={style}>
                <Typography id="modal-modal-title" variant="h6" component="h2" sx={{ mb: 2 }}>
                    Xem bài học
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: "bold" }}>
                    Bài học: {lesson.title}
                </Typography>
                <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: "bold" }}>
                    Nội dung bài học:
                </Typography>
                <Box sx={{ mb: 2 }}>
                    <Typography
                        dangerouslySetInnerHTML={{
                            __html: lesson.content,
                        }}
                    />
                </Box>
                <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: "bold" }}>
                    Kết quả buổi học:
                </Typography>
                <Box sx={{ mb: 2 }}>
                    <Typography
                        dangerouslySetInnerHTML={{
                            __html: lesson.result_summary,
                        }}
                    />
                </Box>
                <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
                    <Box />
                    <Button
                        variant="outlined"
                        color="inherit"
                        sx={{ borderRadius: "10px", textTransform: "none" }}
                        onClick={handleEditModalClose}
                    >
                        Đóng
                    </Button>
                </Box>
            </Box>
        </Modal>
    );
}

function Lesson({ user }) {
    useDocumentTitle("Quản lý bài học | BeE");

    const [loading, setLoading] = useState(true);
    const [lessons, setLessons] = useState([]);
    const [filteredLessons, setFilteredLessons] = useState([]);
    const [classes, setClasses] = useState([]);
    const [selectedClass, setSelectedClass] = useState(null);
    const [openEditModal, setOpenEditModal] = useState(false);
    const [openViewModal, setOpenViewModal] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [editingLesson, setEditingLesson] = useState(null);
    const [requestRefreshLessons, setRequestRefreshLessons] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");

    const handleEditModalOpen = () => setOpenEditModal(true);
    const handleViewModalOpen = () => setOpenViewModal(true);

    const handleEditModalClose = () => {
        setOpenEditModal(false);
        setIsEditing(false);
        setEditingLesson(null);
    };

    const handleViewModalClose = () => {
        setOpenViewModal(false);
        setEditingLesson(null);
    };

    const handleEdit = (id) => {
        const lesson = lessons.find((lesson) => lesson.id === id);
        if (lesson) {
            setEditingLesson(lesson);
            setIsEditing(true);
            handleEditModalOpen();
        }
    };

    const handleView = (id) => {
        const lesson = lessons.find((lesson) => lesson.id === id);
        if (lesson) {
            console.log(lesson);
            setEditingLesson(lesson);
            handleViewModalOpen();
        }
    };

    const handleDelete = (id) => {
        if (window.confirm("Bạn có chắc chắn muốn xóa bài học này?")) {
            axiosInstance
                .delete(`/api/academy/lesson/${id}`)
                .then(() => {
                    setRequestRefreshLessons(true);
                })
                .catch((error) => {
                    console.error("Error deleting lesson:", error);
                    alert("Có lỗi xảy ra khi xóa bài học");
                });
        }
    };

    const handleClassChange = (event, newValue) => {
        setSelectedClass(newValue);
    };

    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value);
    };

    // Fetch classes
    useEffect(() => {
        axiosInstance
            .get("/api/academy/class/")
            .then((response) => {
                setClasses(response.data);
            })
            .catch((error) => {
                console.error("Error fetching classes:", error);
            });
    }, []);

    // Fetch lessons
    useEffect(() => {
        setLoading(true);
        axiosInstance
            .get("/api/academy/lesson/")
            .then((response) => {
                setLessons(response.data);
                setFilteredLessons(response.data);
                setLoading(false);
            })
            .catch((error) => {
                console.error("Error fetching lessons:", error);
                setLoading(false);
            });

        if (requestRefreshLessons) {
            setRequestRefreshLessons(false);
        }
    }, [requestRefreshLessons]);

    // Filter lessons based on selected class and search term
    useEffect(() => {
        let filtered = lessons;

        if (selectedClass) {
            filtered = filtered.filter((lesson) => lesson.class_ref.id === selectedClass.id);
        }

        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            filtered = filtered.filter(
                (lesson) =>
                    lesson.title.toLowerCase().includes(term) ||
                    lesson.content.toLowerCase().includes(term) ||
                    (lesson.result_summary && lesson.result_summary.toLowerCase().includes(term))
            );
        }

        setFilteredLessons(filtered);
    }, [selectedClass, searchTerm, lessons]);

    return (
        <AdminLayout title="Quản lý bài học" user={user}>
            <Box sx={{ display: "flex", gap: 2, flexDirection: "column" }}>
                <Box sx={{ display: "flex", justifyContent: "space-between", mb: "20px", flexWrap: "wrap", gap: 2 }}>
                    <Box sx={{ display: "flex", gap: 2, flexGrow: 1, flexWrap: "wrap" }}>
                        <CustomTextField
                            sx={{ width: { xs: "100%", md: "300px" } }}
                            label="Tìm kiếm"
                            variant="outlined"
                            size="small"
                            value={searchTerm}
                            onChange={handleSearchChange}
                            placeholder="Tìm theo tiêu đề, nội dung..."
                        />
                        <Autocomplete
                            options={classes}
                            getOptionLabel={(option) => option.name}
                            value={selectedClass}
                            onChange={handleClassChange}
                            renderInput={(params) => (
                                <CustomTextField
                                    {...params}
                                    label="Lọc theo lớp"
                                    placeholder="Chọn lớp học"
                                    size="small"
                                />
                            )}
                            sx={{ width: { xs: "100%", md: "300px" } }}
                        />
                    </Box>
                    <CustomButton variant="contained" onClick={handleEditModalOpen} startIcon={<AddIcon />}>
                        Thêm bài học
                    </CustomButton>
                </Box>

                {loading ? (
                    <Loading />
                ) : (
                    <Box>
                        {filteredLessons.length === 0 ? (
                            <Paper sx={{ p: 3, borderRadius: "15px", textAlign: "center" }}>
                                <Typography variant="body1">Không có bài học nào</Typography>
                            </Paper>
                        ) : (
                            <Grid container spacing={2}>
                                {filteredLessons.map((lesson) => (
                                    <Grid size={{ xs: 12, md: 4 }} key={lesson.id}>
                                        <LessonCard
                                            key={lesson.id}
                                            lesson={lesson}
                                            handleEdit={handleEdit}
                                            handleDelete={handleDelete}
                                            handleView={handleView}
                                        />
                                    </Grid>
                                ))}
                            </Grid>
                        )}
                    </Box>
                )}
            </Box>

            <LessonModal
                open={openEditModal}
                handleEditModalClose={handleEditModalClose}
                isEditing={isEditing}
                editingLesson={editingLesson}
                classes={classes}
                setRequestRefreshLessons={setRequestRefreshLessons}
            />

            <LessonModalView
                open={openViewModal}
                handleEditModalClose={handleViewModalClose}
                editingLesson={editingLesson}
                classes={classes}
            />
        </AdminLayout>
    );
}

export default Lesson;
