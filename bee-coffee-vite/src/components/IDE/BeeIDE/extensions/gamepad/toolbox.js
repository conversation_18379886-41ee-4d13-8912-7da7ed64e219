export default {
    id: "gamepad-mqtt",
    kind: "category",
    name: "Gamepad",
    colour: "#00568f",
    contents: [
        // ========== Setup ========== //
        {
            kind: "block",
            type: "gamepad_mqtt_setup",
        },

        // ========== Commands ========== //
        // {
        //     kind: "block",
        //     type: "gamepad_mqtt_read_command",
        // },
        // {
        //     kind: "block",
        //     type: "gamepad_mqtt_last_command",
        // },

        // ========== Button Checks ========== //
        // {
        //     kind: "block",
        //     type: "gamepad_mqtt_button_pressed",
        // },
        // {
        //     kind: "block",
        //     type: "gamepad_mqtt_direction_pressed",
        // },
        // {
        //     kind: "block",
        //     type: "gamepad_mqtt_action_pressed",
        // },

        // ========== Events ========== //
        {
            kind: "block",
            type: "gamepad_mqtt_when_button_pressed",
        },

        // // ========== Safe Loop ========== //
        // {
        //     kind: "block",
        //     type: "gamepad_mqtt_safe_loop",
        //     blockxml: `
        //         <block type="gamepad_mqtt_safe_loop">
        //             <statement name="do">
        //                 <block type="gamepad_mqtt_when_button_pressed">
        //                     <field name="button">UP</field>
        //                     <statement name="do">
        //                         <block type="text_print">
        //                             <value name="TEXT">
        //                                 <shadow type="text">
        //                                     <field name="TEXT">Moving UP!</field>
        //                                 </shadow>
        //                             </value>
        //                         </block>
        //                     </statement>
        //                 </block>
        //             </statement>
        //         </block>
        //     `,
        // },

        // // ========== Utilities ========== //
        // {
        //     kind: "block",
        //     type: "gamepad_mqtt_clear_button",
        // },
        // {
        //     kind: "block",
        //     type: "gamepad_mqtt_clear_all",
        // },
    ],
};
