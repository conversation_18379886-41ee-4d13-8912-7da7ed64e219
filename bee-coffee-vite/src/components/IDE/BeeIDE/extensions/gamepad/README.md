# BeE Gamepad MQTT Extension

This extension allows you to control your BeE board wirelessly using a web-based gamepad interface via MQTT.

## Features

### 🎮 Gamepad MQTT Blocks Available:

1. **Setup Block**

    - `gamepad MQTT setup` - Initialize the gamepad MQTT system with topic configuration

2. **Read Commands**

    - `read gamepad MQTT command` - Read the latest gamepad MQTT command
    - `last gamepad MQTT command` - Get the last command received

3. **Button Status**

    - `gamepad MQTT button [button] is pressed` - Check if a specific button is pressed
    - `gamepad MQTT direction [direction] pressed` - Check direction buttons (UP, DOWN, LEFT, RIGHT)
    - `gamepad MQTT action [action] pressed` - Check action buttons (A, B, X, Y)

4. **Events**

    - `when gamepad MQTT button [button] pressed` - Execute code when a button is pressed

5. **Safe Loop**

    - `gamepad MQTT safe loop` - Non-blocking loop for gamepad control

6. **Utilities**

    - `clear gamepad MQTT button [button]` - Clear specific button state
    - `clear all gamepad MQTT states` - Clear all button states

### 🎯 Supported Buttons:

**Direction Pad:**

-   ↑ UP
-   ↓ DOWN
-   ← LEFT
-   → RIGHT
-   ● CENTER

**Action Buttons:**

-   A, B, X, Y

**Control Buttons:**

-   START, SELECT, STOP

## How to Use

### 1. Setup Your Program

1. Open BeE IDE at `http://localhost:5173/play/bee-ide`
2. Add the "Gamepad MQTT" extension
3. Add your gamepad control logic

### 2. Example Program (Recommended - Safe Loop)

```
when program start
├── connect wifi with SSID "my_wifi" and password "my_pass"
├── if wifi is connected
    └── while loop
        ├── when gamepad button UP pressed
        │   └── print "Moving UP!"
        ├── when gamepad button A pressed
        │   └── LED on with red 255 green 0 blue 0
        └── when gamepad button STOP pressed
            └── print "Gamepad MQTT stopped!"
```

**⚠️ Important:** 
- Connect to WiFi first!
- User and password of MQTT broker must be the same as the one you use in the web interface
- IP address is automatically added to the topic
- Can use `gamepad MQTT safe loop` instead of `forever` to avoid blocking REPL!

### 3. Use the Web Gamepad MQTT

1. Open the gamepad MQTT interface at `http://localhost:5173/play/gamepad`
2. Click "Connect MQTT" and enter your broker details:
   - Broker URL: `beeblock.vn` (or your MQTT broker)
   - Port: `9001` (WebSocket port)
   - Topic: `bee/gamepad/commands` (must match your BeE program)
   - Username/Password: (if required)
3. Upload your program to the BeE board
4. Use the gamepad to control your BeE board wirelessly!

## Technical Details

### MQTT Message Format

The gamepad sends JSON messages in this format:

```json
{
    "command": "up",
    "source": "web-gamepad"
}
```

### Command Values

-   `up`, `down`, `left`, `right`, `center` - Direction commands
-   `a`, `b`, `x`, `y` - Action button commands
-   `start`, `select`, `stop` - Control commands

### Python Code Generated

The extension generates MicroPython code that:

-   Creates a GamepadMQTT class to handle MQTT messages
-   Provides non-blocking command reading via `mqtt.check_msg()`
-   Maintains button state tracking
-   Parses JSON command messages
-   Works with the existing MQTT connection

### Integration

-   Requires MQTT extension to be loaded first
-   Works seamlessly with other BeE board extensions
-   Compatible with LED, sensor, and communication blocks
-   Supports real-time wireless control and feedback

## Tips

1. **Always connect to MQTT first** - Use MQTT blocks before gamepad MQTT setup
2. **Use matching topics** - Ensure web gamepad and BeE program use same topic
3. **Use `gamepad MQTT safe loop` instead of `forever`** - Prevents REPL blocking
4. **Use STOP button to exit loop** - Press STOP on gamepad to safely exit
5. **Test connection** - Verify MQTT connection before uploading gamepad code
6. **Combine with other blocks** - Control LEDs, motors, sensors wirelessly

## Troubleshooting

1. **Gamepad MQTT not responding:**

    - Check MQTT connection in both web gamepad and BeE program
    - Verify broker URL, port, and credentials
    - Ensure topics match exactly
    - Check network connectivity

2. **Commands not recognized:**

    - Verify JSON message format
    - Check MQTT topic subscription
    - Ensure `mqtt.check_msg()` is called regularly

3. **Connection issues:**

    - Use WebSocket port (usually 9001) for web clients
    - Check firewall settings
    - Verify MQTT broker supports WebSocket connections

4. **Performance issues:**

    - Reduce command frequency if needed
    - Use appropriate loop delays
    - Monitor MQTT broker load

## Differences from Serial Gamepad

| Feature | Serial Gamepad | MQTT Gamepad |
|---------|---------------|--------------|
| Connection | USB Serial | WiFi/MQTT |
| Range | Cable length | Network range |
| Setup | Plug & play | MQTT broker required |
| Latency | Very low | Network dependent |
| Multiple devices | One at a time | Multiple possible |
| Offline capability | Yes | Requires network |

## Security Considerations

- Use authentication when possible
- Consider using TLS/SSL for production
- Limit topic access permissions
- Monitor for unauthorized access
- Use unique client IDs
