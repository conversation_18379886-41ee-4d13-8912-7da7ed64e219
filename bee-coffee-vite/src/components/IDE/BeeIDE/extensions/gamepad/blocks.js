import * as Blockly from "blockly";
import { pythonGenerator } from "blockly/python";

import GamepadIcon from "./images/gamepad-mqtt.png";

// ========== GAMEPAD MQTT SETUP ========== //

// Setup gamepad MQTT block
Blockly.Blocks["gamepad_mqtt_setup"] = {
    init: function () {
        this.jsonInit({
            type: "gamepad_mqtt_setup",
            message0: "%{BKY_BEE_GAMEPAD_MQTT_SETUP}", // %1 setup gamepad MQTT
            args0: [
                {
                    type: "field_image",
                    src: GamepadIcon,
                    width: 45,
                    height: 45,
                    alt: "gamepad-mqtt",
                },
            ],
            previousStatement: null,
            nextStatement: null,
            colour: "#00568f",
            tooltip: "Setup gamepad to receive MQTT commands from web interface",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["gamepad_mqtt_setup"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    const code = `gamepad_mqtt = bee.connect_gamepad(wlan.ifconfig()[0])\n`;
    return code;
};

// ========== GAMEPAD MQTT EVENTS ========== //

// When gamepad MQTT button pressed event
Blockly.Blocks["gamepad_mqtt_when_button_pressed"] = {
    init: function () {
        this.jsonInit({
            type: "gamepad_mqtt_when_button_pressed",
            message0: "%{BKY_BEE_GAMEPAD_MQTT_WHEN_BUTTON_PRESSED}", // %2 when gamepad MQTT button %1 pressed
            args0: [
                {
                    type: "field_dropdown",
                    name: "button",
                    options: [
                        ["UP", "UP"],
                        ["DOWN", "DOWN"],
                        ["LEFT", "LEFT"],
                        ["RIGHT", "RIGHT"],
                        ["CENTER", "CENTER"],
                        ["A", "A"],
                        ["B", "B"],
                        ["X", "X"],
                        ["Y", "Y"],
                        ["START", "START"],
                        ["SELECT", "SELECT"],
                        ["STOP", "STOP"],
                    ],
                },
                {
                    type: "field_image",
                    src: GamepadIcon,
                    width: 45,
                    height: 45,
                    alt: "gamepad-mqtt",
                },
            ],
            message1: "%1",
            args1: [
                {
                    type: "input_statement",
                    name: "do",
                },
            ],
            previousStatement: null,
            nextStatement: null,
            colour: "#00568f",
            tooltip: "Execute code when a specific gamepad MQTT button is pressed",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["gamepad_mqtt_when_button_pressed"] = function (block) {
    var button = block.getFieldValue("button");
    var statements_do = pythonGenerator.statementToCode(block, "do");

    var code = `
gamepad_mqtt.check_msg()
if gamepad_mqtt.is_button_pressed("${button}"):
${statements_do}  gamepad_mqtt.clear_button_state("${button}")
`;
    return code;
};

// Export all blocks and generators
export const GamepadMqttBlocks = {
    gamepad_mqtt_setup: Blockly.Blocks["gamepad_mqtt_setup"],
    gamepad_mqtt_when_button_pressed: Blockly.Blocks["gamepad_mqtt_when_button_pressed"],
    // gamepad_mqtt_read_command: Blockly.Blocks["gamepad_mqtt_read_command"],
    // gamepad_mqtt_last_command: Blockly.Blocks["gamepad_mqtt_last_command"],
    // gamepad_mqtt_button_pressed: Blockly.Blocks["gamepad_mqtt_button_pressed"],
    // gamepad_mqtt_direction_pressed: Blockly.Blocks["gamepad_mqtt_direction_pressed"],
    // gamepad_mqtt_action_pressed: Blockly.Blocks["gamepad_mqtt_action_pressed"],
    // gamepad_mqtt_safe_loop: Blockly.Blocks["gamepad_mqtt_safe_loop"],
    // gamepad_mqtt_clear_button: Blockly.Blocks["gamepad_mqtt_clear_button"],
    // gamepad_mqtt_clear_all: Blockly.Blocks["gamepad_mqtt_clear_all"],
};

export const GamepadMqttGenerators = {
    gamepad_mqtt_setup: pythonGenerator["gamepad_mqtt_setup"],
    gamepad_mqtt_when_button_pressed: pythonGenerator["gamepad_mqtt_when_button_pressed"],
    // gamepad_mqtt_read_command: pythonGenerator["gamepad_mqtt_read_command"],
    // gamepad_mqtt_last_command: pythonGenerator["gamepad_mqtt_last_command"],
    // gamepad_mqtt_button_pressed: pythonGenerator["gamepad_mqtt_button_pressed"],
    // gamepad_mqtt_direction_pressed: pythonGenerator["gamepad_mqtt_direction_pressed"],
    // gamepad_mqtt_action_pressed: pythonGenerator["gamepad_mqtt_action_pressed"],
    // gamepad_mqtt_safe_loop: pythonGenerator["gamepad_mqtt_safe_loop"],
    // gamepad_mqtt_clear_button: pythonGenerator["gamepad_mqtt_clear_button"],
    // gamepad_mqtt_clear_all: pythonGenerator["gamepad_mqtt_clear_all"],
};

// ========== GAMEPAD MQTT COMMANDS ========== //

// // Read gamepad MQTT command block
// Blockly.Blocks["gamepad_mqtt_read_command"] = {
//     init: function () {
//         this.jsonInit({
//             type: "gamepad_mqtt_read_command",
//             message0: "%{BKY_BEE_GAMEPAD_MQTT_READ_COMMAND}", // %1 read gamepad MQTT command
//             args0: [
//                 {
//                     type: "field_image",
//                     src: GamepadIcon,
//                     width: 45,
//                     height: 45,
//                     alt: "gamepad-mqtt",
//                 },
//             ],
//             output: "String",
//             colour: "#00568f",
//             tooltip: "Read the latest gamepad MQTT command",
//             helpUrl: "",
//         });
//     },
// };

// pythonGenerator.forBlock["gamepad_mqtt_read_command"] = function (block) {
//     var code = "gamepad_mqtt.read_command()";
//     return [code, pythonGenerator.ORDER_FUNCTION_CALL];
// };

// // Last gamepad MQTT command block
// Blockly.Blocks["gamepad_mqtt_last_command"] = {
//     init: function () {
//         this.jsonInit({
//             type: "gamepad_mqtt_last_command",
//             message0: "%{BKY_BEE_GAMEPAD_MQTT_LAST_COMMAND}", // %1 last gamepad MQTT command
//             args0: [
//                 {
//                     type: "field_image",
//                     src: GamepadIcon,
//                     width: 45,
//                     height: 45,
//                     alt: "gamepad-mqtt",
//                 },
//             ],
//             output: "String",
//             colour: "#00568f",
//             tooltip: "Get the last gamepad MQTT command received",
//             helpUrl: "",
//         });
//     },
// };

// pythonGenerator.forBlock["gamepad_mqtt_last_command"] = function (block) {
//     var code = "gamepad_mqtt.get_last_command()";
//     return [code, pythonGenerator.ORDER_FUNCTION_CALL];
// };

// ========== GAMEPAD MQTT BUTTON CHECKS ========== //

// // Check if gamepad MQTT button is pressed
// Blockly.Blocks["gamepad_mqtt_button_pressed"] = {
//     init: function () {
//         this.jsonInit({
//             type: "gamepad_mqtt_button_pressed",
//             message0: "%{BKY_BEE_GAMEPAD_MQTT_BUTTON_PRESSED}", // %2 gamepad MQTT button %1 is pressed
//             args0: [
//                 {
//                     type: "field_dropdown",
//                     name: "button",
//                     options: [
//                         ["UP", "UP"],
//                         ["DOWN", "DOWN"],
//                         ["LEFT", "LEFT"],
//                         ["RIGHT", "RIGHT"],
//                         ["CENTER", "CENTER"],
//                         ["A", "A"],
//                         ["B", "B"],
//                         ["X", "X"],
//                         ["Y", "Y"],
//                         ["START", "START"],
//                         ["SELECT", "SELECT"],
//                         ["STOP", "STOP"],
//                     ],
//                 },
//                 {
//                     type: "field_image",
//                     src: GamepadIcon,
//                     width: 45,
//                     height: 45,
//                     alt: "gamepad-mqtt",
//                 },
//             ],
//             output: "Boolean",
//             colour: "#00568f",
//             tooltip: "Check if a specific gamepad MQTT button is pressed",
//             helpUrl: "",
//         });
//     },
// };

// pythonGenerator.forBlock["gamepad_mqtt_button_pressed"] = function (block) {
//     var button = block.getFieldValue("button");
//     var code = `gamepad_mqtt.is_button_pressed("${button}")`;
//     return [code, pythonGenerator.ORDER_FUNCTION_CALL];
// };

// // Check gamepad MQTT direction
// Blockly.Blocks["gamepad_mqtt_direction_pressed"] = {
//     init: function () {
//         this.jsonInit({
//             type: "gamepad_mqtt_direction_pressed",
//             message0: "%{BKY_BEE_GAMEPAD_MQTT_DIRECTION_PRESSED}", // %2 gamepad MQTT direction %1 pressed
//             args0: [
//                 {
//                     type: "field_dropdown",
//                     name: "direction",
//                     options: [
//                         ["UP", "UP"],
//                         ["DOWN", "DOWN"],
//                         ["LEFT", "LEFT"],
//                         ["RIGHT", "RIGHT"],
//                         ["CENTER", "CENTER"],
//                     ],
//                 },
//                 {
//                     type: "field_image",
//                     src: GamepadIcon,
//                     width: 45,
//                     height: 45,
//                     alt: "gamepad-mqtt",
//                 },
//             ],
//             output: "Boolean",
//             colour: "#00568f",
//             tooltip: "Check if a gamepad MQTT direction is pressed",
//             helpUrl: "",
//         });
//     },
// };

// pythonGenerator.forBlock["gamepad_mqtt_direction_pressed"] = function (block) {
//     var direction = block.getFieldValue("direction");
//     var code = `gamepad_mqtt.is_button_pressed("${direction}")`;
//     return [code, pythonGenerator.ORDER_FUNCTION_CALL];
// };

// // Check gamepad MQTT action button
// Blockly.Blocks["gamepad_mqtt_action_pressed"] = {
//     init: function () {
//         this.jsonInit({
//             type: "gamepad_mqtt_action_pressed",
//             message0: "%{BKY_BEE_GAMEPAD_MQTT_ACTION_PRESSED}", // %2 gamepad MQTT action %1 pressed
//             args0: [
//                 {
//                     type: "field_dropdown",
//                     name: "action",
//                     options: [
//                         ["A", "A"],
//                         ["B", "B"],
//                         ["X", "X"],
//                         ["Y", "Y"],
//                     ],
//                 },
//                 {
//                     type: "field_image",
//                     src: GamepadIcon,
//                     width: 45,
//                     height: 45,
//                     alt: "gamepad-mqtt",
//                 },
//             ],
//             output: "Boolean",
//             colour: "#00568f",
//             tooltip: "Check if a gamepad MQTT action button is pressed",
//             helpUrl: "",
//         });
//     },
// };

// pythonGenerator.forBlock["gamepad_mqtt_action_pressed"] = function (block) {
//     var action = block.getFieldValue("action");
//     var code = `gamepad_mqtt.is_button_pressed("${action}")`;
//     return [code, pythonGenerator.ORDER_FUNCTION_CALL];
// };

// // ========== GAMEPAD MQTT SAFE LOOP ========== //

// // Gamepad MQTT safe loop block
// Blockly.Blocks["gamepad_mqtt_safe_loop"] = {
//     init: function () {
//         this.jsonInit({
//             type: "gamepad_mqtt_safe_loop",
//             message0: "%{BKY_BEE_GAMEPAD_MQTT_SAFE_LOOP}", // %1 gamepad MQTT safe loop
//             args0: [
//                 {
//                     type: "field_image",
//                     src: GamepadIcon,
//                     width: 45,
//                     height: 45,
//                     alt: "gamepad-mqtt",
//                 },
//             ],
//             message1: "do %1",
//             args1: [
//                 {
//                     type: "input_statement",
//                     name: "do",
//                 },
//             ],
//             previousStatement: null,
//             nextStatement: null,
//             colour: "#00568f",
//             tooltip: "Safe loop for gamepad MQTT control that doesn't block REPL",
//             helpUrl: "",
//         });
//     },
// };

// pythonGenerator.forBlock["gamepad_mqtt_safe_loop"] = function (block) {
//     var statements_do = pythonGenerator.statementToCode(block, "do");

//     var code = `# Gamepad MQTT Safe Loop
// try:
//     while True:
//         # Check for MQTT messages
//         gamepad_mqtt.mqtt.check_msg()

//         # Check for STOP command to exit loop
//         if gamepad_mqtt.is_button_pressed("STOP"):
//             print("Gamepad MQTT loop stopped!")
//             gamepad_mqtt.clear_all_states()
//             break

//         # Execute user code
// ${statements_do}

//         # Small delay to prevent overwhelming the system
//         time.sleep(0.05)

// except KeyboardInterrupt:
//     print("Gamepad MQTT loop interrupted!")
//     gamepad_mqtt.clear_all_states()
// except Exception as e:
//     print(f"Gamepad MQTT loop error: {e}")
//     gamepad_mqtt.clear_all_states()

// `;
//     return code;
// };

// ========== GAMEPAD MQTT UTILITIES ========== //

// // Clear gamepad MQTT button state
// Blockly.Blocks["gamepad_mqtt_clear_button"] = {
//     init: function () {
//         this.jsonInit({
//             type: "gamepad_mqtt_clear_button",
//             message0: "%{BKY_BEE_GAMEPAD_MQTT_CLEAR_BUTTON}", // %2 clear gamepad MQTT button %1
//             args0: [
//                 {
//                     type: "field_dropdown",
//                     name: "button",
//                     options: [
//                         ["UP", "UP"],
//                         ["DOWN", "DOWN"],
//                         ["LEFT", "LEFT"],
//                         ["RIGHT", "RIGHT"],
//                         ["CENTER", "CENTER"],
//                         ["A", "A"],
//                         ["B", "B"],
//                         ["X", "X"],
//                         ["Y", "Y"],
//                         ["START", "START"],
//                         ["SELECT", "SELECT"],
//                         ["STOP", "STOP"],
//                     ],
//                 },
//                 {
//                     type: "field_image",
//                     src: GamepadIcon,
//                     width: 45,
//                     height: 45,
//                     alt: "gamepad-mqtt",
//                 },
//             ],
//             previousStatement: null,
//             nextStatement: null,
//             colour: "#00568f",
//             tooltip: "Clear the state of a specific gamepad MQTT button",
//             helpUrl: "",
//         });
//     },
// };

// pythonGenerator.forBlock["gamepad_mqtt_clear_button"] = function (block) {
//     var button = block.getFieldValue("button");
//     var code = `gamepad_mqtt.clear_button_state("${button}")\n`;
//     return code;
// };

// // Clear all gamepad MQTT states
// Blockly.Blocks["gamepad_mqtt_clear_all"] = {
//     init: function () {
//         this.jsonInit({
//             type: "gamepad_mqtt_clear_all",
//             message0: "%{BKY_BEE_GAMEPAD_MQTT_CLEAR_ALL}", // %1 clear all gamepad MQTT states
//             args0: [
//                 {
//                     type: "field_image",
//                     src: GamepadIcon,
//                     width: 45,
//                     height: 45,
//                     alt: "gamepad-mqtt",
//                 },
//             ],
//             previousStatement: null,
//             nextStatement: null,
//             colour: "#00568f",
//             tooltip: "Clear all gamepad MQTT button states and command queue",
//             helpUrl: "",
//         });
//     },
// };

// pythonGenerator.forBlock["gamepad_mqtt_clear_all"] = function (block) {
//     var code = `gamepad_mqtt.clear_all_states()\n`;
//     return code;
// };
