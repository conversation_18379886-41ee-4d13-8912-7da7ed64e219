import * as Blockly from "blockly";
import { pythonGenerator } from "blockly/python";

import MqttIcon from "./images/mqtt.png";

// Define blocks
Blockly.Blocks["mqtt_connect"] = {
    init: function () {
        this.jsonInit({
            type: "mqtt_connect",
            message0: "%{BKY_BEE_MQTT_CONNECT}", //%3 connect mqtt url %1 port %2
            args0: [
                {
                    type: "field_input",
                    name: "mqtt_url",
                    text: "beeblock.vn",
                },
                {
                    type: "field_input",
                    name: "mqtt_port",
                    text: "1883",
                },
                {
                    type: "field_image",
                    src: MqttIcon,
                    width: 40,
                    height: 40,
                    alt: "mqtt",
                },
            ],
            message1: "user %1 password %2",
            args1: [
                {
                    type: "field_input",
                    name: "mqtt_user",
                    text: "my_user",
                },
                {
                    type: "field_input",
                    name: "mqtt_pass",
                    text: "my_pass",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#9400ab",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["mqtt_connect"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";

    var mqtt_url = block.getFieldValue("mqtt_url");
    var mqtt_port = block.getFieldValue("mqtt_port");
    var mqtt_user = block.getFieldValue("mqtt_user");
    var mqtt_pass = block.getFieldValue("mqtt_pass");

    if (mqtt_url === "" || mqtt_port === "" || mqtt_user === "" || mqtt_pass === "") {
        alert("Please fill in all the fields!");
        return;
    }

    var code = `mqtt = bee.connect_mqtt('${mqtt_url}', ${mqtt_port}, '${mqtt_user}', '${mqtt_pass}')\n`;
    return code;
};

// Define blocks
Blockly.Blocks["publish_mqtt"] = {
    init: function () {
        this.jsonInit({
            type: "publish_mqtt",
            message0: "%{BKY_BEE_MQTT_PUBLISH}", //%3 publish %1 data %2
            args0: [
                {
                    type: "field_input",
                    name: "mqtt_topic",
                    text: "topic",
                },
                {
                    type: "input_value",
                    name: "mqtt_data",
                },
                {
                    type: "field_image",
                    src: MqttIcon,
                    width: 40,
                    height: 40,
                    alt: "mqtt",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#9400ab",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["publish_mqtt"] = function (block) {
    var mqtt_topic = block.getFieldValue("mqtt_topic");
    var mqtt_data = pythonGenerator.valueToCode(block, "mqtt_data", pythonGenerator.ORDER_ATOMIC);
    var code = `mqtt.publish('${mqtt_topic}', ${mqtt_data}, qos=0)\n`;
    return code;
};

// Define blocks
Blockly.Blocks["mqtt_connect_server"] = {
    init: function () {
        this.jsonInit({
            type: "mqtt_connect_server",
            message0: "%{BKY_BEE_MQTT_CONNECT_SERVER}", //%1 connect server
            args0: [
                {
                    type: "field_image",
                    src: MqttIcon,
                    width: 40,
                    height: 40,
                    alt: "mqtt",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#9400ab",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["mqtt_connect_server"] = function (block) {
    var code = `mqtt.connect()\n`;
    return code;
};

// Define blocks
Blockly.Blocks["mqtt_subscribe_topic"] = {
    init: function () {
        this.jsonInit({
            type: "mqtt_subscribe_topic",
            message0: "%{BKY_BEE_MQTT_SUBSCRIBE_TOPIC}", //%2 subscribe topic %1
            args0: [
                {
                    type: "field_input",
                    name: "mqtt_topic",
                    text: "topic",
                },
                {
                    type: "field_image",
                    src: MqttIcon,
                    width: 40,
                    height: 40,
                    alt: "mqtt",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#9400ab",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["mqtt_subscribe_topic"] = function (block) {
    var mqtt_topic = block.getFieldValue("mqtt_topic");
    var code = `mqtt.subscribe('${mqtt_topic}')\n`;
    return code;
};

// Define blocks
Blockly.Blocks["mqtt_on_receive"] = {
    init: function () {
        this.jsonInit({
            type: "mqtt_on_receive",
            message0: "%{BKY_BEE_MQTT_ON_RECEIVE}", //%3 on receive %1 %2
            args0: [
                {
                    type: "input_dummy",
                },
                {
                    type: "input_statement",
                    name: "callback",
                },
                {
                    type: "field_image",
                    src: MqttIcon,
                    width: 40,
                    height: 40,
                    alt: "mqtt",
                },
            ],
            previousStatement: null,
            nextStatement: null,
            colour: "#9400ab",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["mqtt_on_receive"] = function (block) {
    var statements_callback = pythonGenerator.statementToCode(block, "callback");
    statements_callback = statements_callback.length > 0 ? statements_callback : pythonGenerator.INDENT + "pass";

    var functionName = pythonGenerator.provideFunction_("mqtt_sub_cb", [
        "def " + pythonGenerator.FUNCTION_NAME_PLACEHOLDER_ + "(topic, msg):",
        statements_callback,
    ]);

    var code = `mqtt.set_callback(${functionName})\n`;
    return code;
};

// Define blocks
Blockly.Blocks["mqtt_check_msg"] = {
    init: function () {
        this.jsonInit({
            type: "mqtt_check_msg",
            message0: "%{BKY_BEE_MQTT_CHECK_MSG}", //%1 check message
            args0: [
                {
                    type: "field_image",
                    src: MqttIcon,
                    width: 40,
                    height: 40,
                    alt: "mqtt",
                },
            ],
            previousStatement: null,
            nextStatement: null,
            colour: "#9400ab",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["mqtt_check_msg"] = function (block) {
    var code = `mqtt.check_msg()\n`;
    return code;
};

// Define blocks
Blockly.Blocks["mqtt_get_topic"] = {
    init: function () {
        this.jsonInit({
            type: "mqtt_get_topic",
            message0: "%{BKY_BEE_MQTT_GET_TOPIC}", //%1 get receive topic
            args0: [
                {
                    type: "field_image",
                    src: MqttIcon,
                    width: 40,
                    height: 40,
                    alt: "mqtt",
                },
            ],
            output: null,
            colour: "#9400ab",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["mqtt_get_topic"] = function (block) {
    var code = 'topic.decode("utf-8")';
    return [code, pythonGenerator.ORDER_NONE];
};

// Define blocks
Blockly.Blocks["mqtt_get_message"] = {
    init: function () {
        this.jsonInit({
            type: "mqtt_get_message",
            message0: "%{BKY_BEE_MQTT_GET_MESSAGE}", //%1 get receive message
            args0: [
                {
                    type: "field_image",
                    src: MqttIcon,
                    width: 40,
                    height: 40,
                    alt: "mqtt",
                },
            ],
            output: "String",
            colour: "#9400ab",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["mqtt_get_message"] = function (block) {
    var code = 'msg.decode("utf-8")';
    return [code, pythonGenerator.ORDER_NONE];
};

// Export all blocks and generators
export const MqttBlocks = {
    mqtt_connect: Blockly.Blocks["mqtt_connect"],
    publish_mqtt: Blockly.Blocks["publish_mqtt"],
    mqtt_connect_server: Blockly.Blocks["mqtt_connect_server"],
    mqtt_subscribe_topic: Blockly.Blocks["mqtt_subscribe_topic"],
    mqtt_on_receive: Blockly.Blocks["mqtt_on_receive"],
    mqtt_check_msg: Blockly.Blocks["mqtt_check_msg"],
    mqtt_get_topic: Blockly.Blocks["mqtt_get_topic"],
    mqtt_get_message: Blockly.Blocks["mqtt_get_message"],
};

export const MqttGenerators = {
    mqtt_connect: pythonGenerator["mqtt_connect"],
    publish_mqtt: pythonGenerator["publish_mqtt"],
    mqtt_connect_server: pythonGenerator["mqtt_connect_server"],
    mqtt_subscribe_topic: pythonGenerator["mqtt_subscribe_topic"],
    mqtt_on_receive: pythonGenerator["mqtt_on_receive"],
    mqtt_check_msg: pythonGenerator["mqtt_check_msg"],
    mqtt_get_topic: pythonGenerator["mqtt_get_topic"],
    mqtt_get_message: pythonGenerator["mqtt_get_message"],
};
