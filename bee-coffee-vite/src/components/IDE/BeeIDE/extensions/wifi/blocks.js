import * as Blockly from "blockly";
import { pythonGenerator } from "blockly/python";

import WifiIcon from "./images/wifi.png";

// Define blocks
Blockly.Blocks["wifi_connect"] = {
    init: function () {
        this.jsonInit({
            type: "wifi_connect",
            message0: "%{BKY_BEE_WIFI_CONNECT}", //%3 connect wifi %1 password %2
            args0: [
                {
                    type: "field_input",
                    name: "wifi_ssid",
                    text: "my_wifi",
                },
                {
                    type: "field_input",
                    name: "wifi_pass",
                    text: "my_pass",
                },
                {
                    type: "field_image",
                    src: WifiIcon,
                    width: 45,
                    height: 45,
                    alt: "wifi",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#f500ab",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["wifi_connect"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var wifi_ssid = block.getFieldValue("wifi_ssid");
    var wifi_pass = block.getFieldValue("wifi_pass");
    var code = `wlan = bee.connect_wifi('${wifi_ssid}', '${wifi_pass}')\n`;
    return code;
};

// Define blocks
Blockly.Blocks["wifi_is_connected"] = {
    init: function () {
        this.jsonInit({
            type: "wifi_is_connected",
            message0: "%{BKY_BEE_WIFI_IS_CONNECTED}", //%1 is connected
            args0: [
                {
                    type: "field_image",
                    src: WifiIcon,
                    width: 45,
                    height: 45,
                    alt: "wifi",
                },
            ],
            inputsInline: true,
            output: ["Boolean", "Number"],
            colour: "#f500ab",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["wifi_is_connected"] = function (block) {
    var code = "wlan.isconnected()";
    return [code, pythonGenerator.ORDER_NONE];
};

// Define blocks
Blockly.Blocks["wifi_ip_address"] = {
    init: function () {
        this.jsonInit({
            type: "wifi_ip_address",
            message0: "%{BKY_BEE_WIFI_IP_ADDRESS}", //%1 ip address
            args0: [
                {
                    type: "field_image",
                    src: WifiIcon,
                    width: 45,
                    height: 45,
                    alt: "wifi",
                },
            ],
            inputsInline: true,
            output: ["String"],
            colour: "#f500ab",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["wifi_ip_address"] = function (block) {
    var code = "wlan.ifconfig()[0]";
    return [code, pythonGenerator.ORDER_NONE];
};

// Export all blocks and generators
export const WifiBlocks = {
    wifi_connect: Blockly.Blocks["wifi_connect"],
    wifi_is_connected: Blockly.Blocks["wifi_is_connected"],
    wifi_ip_address: Blockly.Blocks["wifi_ip_address"],
};

export const WifiGenerators = {
    wifi_connect: pythonGenerator["wifi_connect"],
    wifi_is_connected: pythonGenerator["wifi_is_connected"],
    wifi_ip_address: pythonGenerator["wifi_ip_address"],
};
