/* BeE Gamepad MQTT Styles */
.bee-gamepad-mqtt-container {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    color: white;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    overflow-x: hidden;
    position: relative;
}

.bee-gamepad-mqtt-container::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(156, 39, 176, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(233, 30, 99, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 87, 34, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.bee-gamepad-mqtt-container>* {
    position: relative;
    z-index: 1;
}

.gamepad-main {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.gamepad-header {
    text-align: center;
    margin-bottom: 30px;
}

.gamepad-title {
    font-size: 2.5rem;
    font-weight: bold;
    background: linear-gradient(45deg, #9c27b0, #e91e63, #ff5722);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.gamepad-subtitle {
    font-size: 1.1rem;
    color: #b0b0b0;
    margin-bottom: 20px;
}

.connection-status {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    flex-wrap: wrap;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    transition: all 0.3s ease;
    margin-right: 20px;
}

.status-indicator.connected {
    background: rgba(76, 175, 80, 0.2);
    border: 2px solid #4caf50;
}

.status-indicator.disconnected {
    background: rgba(244, 67, 54, 0.2);
    border: 2px solid #f44336;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-indicator.connected .status-dot {
    background: #4caf50;
}

.status-indicator.disconnected .status-dot {
    background: #f44336;
}

.connection-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 0.9rem;
    color: #b0b0b0;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

.gamepad-layout {
    display: flex;
    justify-content: space-between;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    margin-bottom: 40px;
    justify-items: center;
    align-items: center;
}

.left-controls,
.right-controls {
    display: flex;
    justify-content: center;
    align-items: center;
}

.dpad-container,
.action-container {
    position: relative;
}

.dpad {
    display: grid;
    grid-template-columns: repeat(3, 70px);
    grid-template-rows: repeat(3, 70px);
    gap: 3px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.dpad-btn {
    background: linear-gradient(145deg, #333, #555);
    border: 2px solid #666;
    color: white;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.15s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    touch-action: manipulation;
}

.dpad-btn:hover {
    background: linear-gradient(145deg, #444, #666);
    transform: translateY(-2px);
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(156, 39, 176, 0.4);
}

.dpad-btn.pressed {
    background: linear-gradient(145deg, #9c27b0, #e91e63);
    transform: translateY(2px);
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(156, 39, 176, 0.8);
}

.dpad-btn.center {
    background: linear-gradient(145deg, #444, #666);
    border-color: #888;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(3, 70px);
    grid-template-rows: repeat(3, 70px);
    gap: 5px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.action-btn {
    background: linear-gradient(145deg, #333, #555);
    border: 3px solid #666;
    color: white;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.15s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    touch-action: manipulation;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(233, 30, 99, 0.4);
}

.action-btn.pressed {
    transform: translateY(2px);
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(233, 30, 99, 0.8);
}

.action-btn.a {
    background: linear-gradient(145deg, #4caf50, #66bb6a);
    border-color: #4caf50;
}

.action-btn.b {
    background: linear-gradient(145deg, #f44336, #ef5350);
    border-color: #f44336;
}

.action-btn.x {
    background: linear-gradient(145deg, #2196f3, #42a5f5);
    border-color: #2196f3;
}

.action-btn.y {
    background: linear-gradient(145deg, #ff9800, #ffb74d);
    border-color: #ff9800;
}

.control-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    /* margin-bottom: 0px; */
    flex-wrap: wrap;
}

.control-btn {
    background: linear-gradient(145deg, #333, #555);
    border: 2px solid #666;
    color: white;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    border-radius: 25px;
    transition: all 0.15s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    touch-action: manipulation;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: linear-gradient(145deg, #444, #666);
    transform: translateY(-2px);
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(255, 87, 34, 0.4);
}

.control-btn.pressed {
    background: linear-gradient(145deg, #ff5722, #ff7043);
    transform: translateY(2px);
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(255, 87, 34, 0.8);
}

.control-btn.select {
    background: linear-gradient(145deg, #9e9e9e, #bdbdbd);
}

.control-btn.start {
    background: linear-gradient(145deg, #3be995, #14fc5a);
}

.control-btn.stop {
    background: linear-gradient(145deg, #f44336, #ef5350);
}

.instructions {
    margin-bottom: 40px;
}

.instructions h3 {
    text-align: center;
    font-size: 1.8rem;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #9c27b0, #e91e63);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.instructions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.instruction-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.instruction-card:hover {
    transform: translateY(-5px);
}

.instruction-card h4 {
    color: #e91e63;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.instruction-card p {
    color: #b0b0b0;
    line-height: 1.5;
}

.keyboard-shortcuts {
    margin-bottom: 40px;
}

.keyboard-shortcuts h3 {
    text-align: center;
    font-size: 1.8rem;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #2196f3, #00bcd4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.shortcut-group {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.shortcut-group h4 {
    color: #00bcd4;
    margin-bottom: 15px;
    text-align: center;
    font-size: 1.1rem;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 5px 0;
}

.key {
    background: linear-gradient(145deg, #333, #555);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-family: monospace;
    font-size: 0.9rem;
    border: 1px solid #666;
    min-width: 40px;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .gamepad-layout {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .gamepad-title {
        font-size: 2rem;
    }

    .connection-status {
        display: flex;
        flex-direction: row;
        justify-content: center;
        gap: 10px;
    }

    .dpad {
        grid-template-columns: repeat(3, 60px);
        grid-template-rows: repeat(3, 60px);
    }

    .action-buttons {
        grid-template-columns: repeat(3, 60px);
        grid-template-rows: repeat(3, 60px);
    }

    .instructions-grid {
        grid-template-columns: 1fr;
    }

    .shortcuts-grid {
        grid-template-columns: 1fr;
    }

    .control-buttons {
        flex-direction: row;
        gap: 15px;
        align-items: center;
    }

    .control-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
        width: 120px;
    }
}

@media (max-width: 480px) {
    .gamepad-main {
        padding: 15px;
    }

    .gamepad-title {
        font-size: 1.8rem;
    }

    .dpad {
        grid-template-columns: repeat(3, 55px);
        grid-template-rows: repeat(3, 55px);
        gap: 2px;
    }

    .action-buttons {
        grid-template-columns: repeat(3, 55px);
        grid-template-rows: repeat(3, 55px);
        gap: 3px;
    }

    .dpad-btn {
        font-size: 20px;
    }

    .action-btn {
        font-size: 18px;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {

    .dpad-btn,
    .action-btn,
    .control-btn {
        min-height: 44px;
        min-width: 44px;
    }

    .dpad-btn:active,
    .action-btn:active,
    .control-btn:active {
        transform: scale(0.95);
    }
}