import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>witch, FormControlLabel, Divider, Box } from "@mui/material";
import BeeAppBarMQTT from "./BeeAppBarMQTT";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";
import "./BeeGamepadMQTT.css";
import mqtt from "mqtt";

class MQTTClient {
    constructor() {
        this.client = null;
        this.clientId = `gamepad_${Math.random().toString(16).substr(2, 8)}`;
    }

    connect(brokerUrl, options = {}) {
        return new Promise((resolve, reject) => {
            const wsUrl = `mqtts://${brokerUrl}:${options.port || 8883}`;

            this.client = mqtt.connect(wsUrl, {
                clientId: this.clientId,
                keepalive: 30,
                clean: true,
                ...options
            });

            this.client.on('connect', () => {
                console.log('Connected to MQTT broker');
                resolve({ success: true, message: 'Connected successfully!' });
            });

            this.client.on('error', (error) => {
                console.error('Connection error:', error);
                reject({ success: false, message: 'MQTT connection failed' });
            });

            this.client.on('message', (topic, message) => {
                console.log(`Received on ${topic}:`, message.toString());
            });
        });
    }

    publish(topic, message) {
        if (!this.client?.connected) {
            throw new Error('MQTT client not connected');
        }
        this.client.publish(topic, message);
    }

    subscribe(topic) {
        if (!this.client?.connected) {
            throw new Error('MQTT client not connected');
        }
        this.client.subscribe(topic);
    }

    disconnect() {
        if (this.client) {
            this.client.end();
        }
    }
}

function BeeGamepadMQTT({ user }) {
    useDocumentTitle("BeE Gamepad Controller");

    const [project, setProject] = useState({
        mqttConnected: false,
        mqttClient: new MQTTClient(),
        brokerUrl: '',
        port: '',
        username: '',
        password: '',
        topic: ''
    });

    const [message, setMessage] = useState("");
    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [isPressed, setIsPressed] = useState({});
    const [continuousMode, setContinuousMode] = useState(false);
    const intervalRef = useRef(null);

    // Handle MQTT connection
    const handleConnectMQTT = async (connectionData) => {
        try {
            const result = await project.mqttClient.connect(connectionData.brokerUrl, {
                port: parseInt(connectionData.port),
                username: connectionData.username,
                password: connectionData.password
            });

            setProject((prev) => ({
                ...prev,
                mqttConnected: true,
                brokerUrl: connectionData.brokerUrl,
                port: connectionData.port,
                username: connectionData.username,
                password: connectionData.password,
                topic: connectionData.topic + connectionData.target_ip
            }));

            setMessage(result.message);
            setSnackbarOpen(true);
        } catch (error) {
            setMessage(error.message);
            setSnackbarOpen(true);
        }
    };

    // Handle MQTT disconnection
    const handleDisconnectMQTT = () => {
        project.mqttClient.disconnect();
        setProject((prev) => ({
            ...prev,
            mqttConnected: false
        }));
        setMessage("Disconnected from MQTT broker");
        setSnackbarOpen(true);
    };

    // Send command via MQTT
    const sendCommand = async (command) => {
        if (!project.mqttConnected) {
            setMessage("Please connect to MQTT broker first!");
            setSnackbarOpen(true);
            return;
        }

        try {
            const commandData = {
                command: command.toLowerCase(),
                // timestamp: Date.now(),
                source: 'web-gamepad'
            };

            project.mqttClient.publish(project.topic, JSON.stringify(commandData));
        } catch (error) {
            console.error("Error sending MQTT command:", error);
            setMessage("Error sending command: " + error.message);
            setSnackbarOpen(true);
        }
    };

    // Handle button press
    const handleButtonPress = (command) => {
        setIsPressed((prev) => ({ ...prev, [command]: true }));
        sendCommand(command);

        if (continuousMode) {
            intervalRef.current = setInterval(() => {
                console.log("Sending command in continuous mode");
                sendCommand(command);
            }, 100);
        }
    };

    // Handle button release
    const handleButtonRelease = (command) => {
        setIsPressed((prev) => ({ ...prev, [command]: false }));
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }

        if (continuousMode) {
            sendCommand("STOP");
        }
    };

    // Keyboard controls
    useEffect(() => {
        const keyMap = {
            ArrowUp: "UP",
            ArrowDown: "DOWN",
            ArrowLeft: "LEFT",
            ArrowRight: "RIGHT",
            KeyW: "UP",
            KeyS: "DOWN",
            KeyA: "LEFT",
            KeyD: "RIGHT",
            Space: "CENTER",
            KeyZ: "A",
            KeyX: "B",
            KeyC: "X",
            KeyV: "Y",
            Enter: "START",
            ShiftLeft: "SELECT",
            Escape: "STOP"
        };

        const handleKeyDown = (event) => {
            const command = keyMap[event.code];
            if (command && !isPressed[command]) {
                event.preventDefault();
                handleButtonPress(command);
            }
        };

        const handleKeyUp = (event) => {
            const command = keyMap[event.code];
            if (command && isPressed[command]) {
                event.preventDefault();
                handleButtonRelease(command);
            }
        };

        window.addEventListener("keydown", handleKeyDown);
        window.addEventListener("keyup", handleKeyUp);

        return () => {
            window.removeEventListener("keydown", handleKeyDown);
            window.removeEventListener("keyup", handleKeyUp);
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [isPressed, continuousMode]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (project.mqttClient) {
                project.mqttClient.disconnect();
            }
        };
    }, []);

    return (
        <div className="bee-gamepad-mqtt-container">
            <BeeAppBarMQTT
                project={project}
                handleConnectMQTT={handleConnectMQTT}
                handleDisconnectMQTT={handleDisconnectMQTT}
                message={message}
                user={user}
            />

            <div className="gamepad-main">
                {/* Header */}
                <div className="gamepad-header">
                    <h1 className="gamepad-title">BeE Gamepad Controller</h1>
                    <p className="gamepad-subtitle">
                        Control your BeE board wirelessly. Use keyboard or click buttons.
                    </p>
                </div>

                {/* Connection Status */}
                <div className="connection-status">
                    <div className={`status-indicator ${project.mqttConnected ? "connected" : "disconnected"}`}>
                        <div className="status-dot"></div>
                        <span>{project.mqttConnected ? project.topic.split('/')[3] : "Gamepad Disconnected"}</span>
                    </div>

                    {/* {project.mqttConnected && (
                        <div className="connection-info">
                            <span>Broker: {project.brokerUrl}:{project.port}</span>
                            <span>Topic: {project.topic}</span>
                        </div>
                    )} */}

                    <FormControlLabel
                        control={
                            <Switch
                                checked={continuousMode}
                                onChange={(e) => setContinuousMode(e.target.checked)}
                                color="primary"
                            />
                        }
                        label="Continuous Mode"
                        sx={{ color: "white" }}
                    />
                </div>

                {/* <Divider sx={{ mb: 4, backgroundColor: "white" }} /> */}
                <Box sx={{ p: 2, border: "1px solid white", borderRadius: "20px", mb: 4 }}>
                    {/* Gamepad Layout */}
                    <div className="gamepad-layout">
                        {/* Left Controls - D-Pad */}
                        <div className="left-controls">
                            <div className="dpad-container">
                                <div className="dpad">
                                    <div></div>
                                    <div
                                        className={`dpad-btn ${isPressed.UP ? "pressed" : ""}`}
                                        // onMouseDown={() => handleButtonPress("UP")}
                                        // onMouseUp={() => handleButtonRelease("UP")}
                                        // onMouseLeave={() => handleButtonRelease("UP")}
                                        // onTouchStart={() => handleButtonPress("UP")}
                                        // onTouchEnd={() => handleButtonRelease("UP")}
                                        onPointerDown={() => handleButtonPress("UP")}
                                        onPointerUp={() => handleButtonRelease("UP")}
                                    >
                                        ↑
                                    </div>
                                    <div></div>
                                    <div
                                        className={`dpad-btn ${isPressed.LEFT ? "pressed" : ""}`}
                                        onMouseDown={() => handleButtonPress("LEFT")}
                                        onMouseUp={() => handleButtonRelease("LEFT")}
                                        onMouseLeave={() => handleButtonRelease("LEFT")}
                                        onTouchStart={() => handleButtonPress("LEFT")}
                                        onTouchEnd={() => handleButtonRelease("LEFT")}
                                    >
                                        ←
                                    </div>
                                    <div
                                        className={`dpad-btn center ${isPressed.CENTER ? "pressed" : ""}`}
                                        onMouseDown={() => handleButtonPress("CENTER")}
                                        onMouseUp={() => handleButtonRelease("CENTER")}
                                        onMouseLeave={() => handleButtonRelease("CENTER")}
                                        onTouchStart={() => handleButtonPress("CENTER")}
                                        onTouchEnd={() => handleButtonRelease("CENTER")}
                                    >
                                        ●
                                    </div>
                                    <div
                                        className={`dpad-btn ${isPressed.RIGHT ? "pressed" : ""}`}
                                        onMouseDown={() => handleButtonPress("RIGHT")}
                                        onMouseUp={() => handleButtonRelease("RIGHT")}
                                        onMouseLeave={() => handleButtonRelease("RIGHT")}
                                        onTouchStart={() => handleButtonPress("RIGHT")}
                                        onTouchEnd={() => handleButtonRelease("RIGHT")}
                                    >
                                        →
                                    </div>
                                    <div></div>
                                    <div
                                        className={`dpad-btn ${isPressed.DOWN ? "pressed" : ""}`}
                                        onMouseDown={() => handleButtonPress("DOWN")}
                                        onMouseUp={() => handleButtonRelease("DOWN")}
                                        onMouseLeave={() => handleButtonRelease("DOWN")}
                                        onTouchStart={() => handleButtonPress("DOWN")}
                                        onTouchEnd={() => handleButtonRelease("DOWN")}
                                    >
                                        ↓
                                    </div>
                                    <div></div>
                                </div>
                            </div>
                        </div>

                        {/* Right Controls - Action Buttons */}
                        <div className="right-controls">
                            <div className="action-container">
                                <div className="action-buttons">
                                    <div></div>
                                    <div
                                        className={`action-btn y ${isPressed.Y ? "pressed" : ""}`}
                                        onMouseDown={() => handleButtonPress("Y")}
                                        onMouseUp={() => handleButtonRelease("Y")}
                                        onMouseLeave={() => handleButtonRelease("Y")}
                                        onTouchStart={() => handleButtonPress("Y")}
                                        onTouchEnd={() => handleButtonRelease("Y")}
                                    >
                                        Y
                                    </div>
                                    <div></div>
                                    <div
                                        className={`action-btn x ${isPressed.X ? "pressed" : ""}`}
                                        onMouseDown={() => handleButtonPress("X")}
                                        onMouseUp={() => handleButtonRelease("X")}
                                        onMouseLeave={() => handleButtonRelease("X")}
                                        onTouchStart={() => handleButtonPress("X")}
                                        onTouchEnd={() => handleButtonRelease("X")}
                                    >
                                        X
                                    </div>
                                    <div></div>
                                    <div
                                        className={`action-btn a ${isPressed.A ? "pressed" : ""}`}
                                        onMouseDown={() => handleButtonPress("A")}
                                        onMouseUp={() => handleButtonRelease("A")}
                                        onMouseLeave={() => handleButtonRelease("A")}
                                        onTouchStart={() => handleButtonPress("A")}
                                        onTouchEnd={() => handleButtonRelease("A")}
                                    >
                                        A
                                    </div>
                                    <div></div>
                                    <div
                                        className={`action-btn b ${isPressed.B ? "pressed" : ""}`}
                                        onMouseDown={() => handleButtonPress("B")}
                                        onMouseUp={() => handleButtonRelease("B")}
                                        onMouseLeave={() => handleButtonRelease("B")}
                                        onTouchStart={() => handleButtonPress("B")}
                                        onTouchEnd={() => handleButtonRelease("B")}
                                    >
                                        B
                                    </div>
                                    <div></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Control Buttons */}
                    <div className="control-buttons">
                        <div
                            className={`control-btn select ${isPressed.SELECT ? "pressed" : ""}`}
                            onMouseDown={() => handleButtonPress("SELECT")}
                            onMouseUp={() => handleButtonRelease("SELECT")}
                            onMouseLeave={() => handleButtonRelease("SELECT")}
                            onTouchStart={() => handleButtonPress("SELECT")}
                            onTouchEnd={() => handleButtonRelease("SELECT")}
                        >
                            SELECT
                        </div>
                        <div
                            className={`control-btn start ${isPressed.START ? "pressed" : ""}`}
                            onMouseDown={() => handleButtonPress("START")}
                            onMouseUp={() => handleButtonRelease("START")}
                            onMouseLeave={() => handleButtonRelease("START")}
                            onTouchStart={() => handleButtonPress("START")}
                            onTouchEnd={() => handleButtonRelease("START")}
                        >
                            START
                        </div>
                        <div
                            className={`control-btn stop ${isPressed.STOP ? "pressed" : ""}`}
                            onMouseDown={() => handleButtonPress("STOP")}
                            onMouseUp={() => handleButtonRelease("STOP")}
                            onMouseLeave={() => handleButtonRelease("STOP")}
                            onTouchStart={() => handleButtonPress("STOP")}
                            onTouchEnd={() => handleButtonRelease("STOP")}
                        >
                            STOP
                        </div>
                    </div>
                </Box>

                {/* <Divider sx={{ mb: 4, backgroundColor: "white" }} /> */}

                {/* Keyboard Shortcuts */}
                <div className="keyboard-shortcuts">
                    <h3>Keyboard Shortcuts</h3>
                    <div className="shortcuts-grid">
                        <div className="shortcut-group">
                            <h4>Movement</h4>
                            <div className="shortcut-item">
                                <span className="key">↑ W</span>
                                <span>UP</span>
                            </div>
                            <div className="shortcut-item">
                                <span className="key">↓ S</span>
                                <span>DOWN</span>
                            </div>
                            <div className="shortcut-item">
                                <span className="key">← A</span>
                                <span>LEFT</span>
                            </div>
                            <div className="shortcut-item">
                                <span className="key">→ D</span>
                                <span>RIGHT</span>
                            </div>
                        </div>
                        <div className="shortcut-group">
                            <h4>Actions</h4>
                            <div className="shortcut-item">
                                <span className="key">Z</span>
                                <span>A Button</span>
                            </div>
                            <div className="shortcut-item">
                                <span className="key">X</span>
                                <span>B Button</span>
                            </div>
                            <div className="shortcut-item">
                                <span className="key">C</span>
                                <span>X Button</span>
                            </div>
                            <div className="shortcut-item">
                                <span className="key">V</span>
                                <span>Y Button</span>
                            </div>
                        </div>
                        <div className="shortcut-group">
                            <h4>Controls</h4>
                            <div className="shortcut-item">
                                <span className="key">Enter</span>
                                <span>START</span>
                            </div>
                            <div className="shortcut-item">
                                <span className="key">Shift</span>
                                <span>SELECT</span>
                            </div>
                            <div className="shortcut-item">
                                <span className="key">Esc</span>
                                <span>STOP</span>
                            </div>
                            <div className="shortcut-item">
                                <span className="key">Space</span>
                                <span>CENTER</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <Snackbar
                open={snackbarOpen}
                autoHideDuration={3000}
                onClose={() => setSnackbarOpen(false)}
                anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
            >
                <Alert
                    onClose={() => setSnackbarOpen(false)}
                    severity={message.includes("Error") || message.includes("failed") ? "error" : "success"}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
}

export default BeeGamepadMQTT;
