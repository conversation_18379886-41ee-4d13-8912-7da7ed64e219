import React from "react";
import { Box, Typography, Container, Paper, useTheme, Divider } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useNavigate } from "react-router-dom";
import CodeIcon from "@mui/icons-material/Code";
import SpeedIcon from "@mui/icons-material/Speed";
import BuildIcon from "@mui/icons-material/Build";
import SchoolIcon from "@mui/icons-material/School";
import DashboardCustomizeIcon from "@mui/icons-material/DashboardCustomize";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import SportsEsportsIcon from "@mui/icons-material/SportsEsports";

import { keyframes } from "@mui/system";
import { motion } from "framer-motion";
import { useDocumentTitle } from "../../hooks/useDocumentTitle";
import Footer from "../Common/Footer";

const ide_list = [
    {
        name: "Scratch IDE",
        description: "Perfect for beginners. Learn programming concepts through visual blocks.",
        image: "/scratch-cat.svg",
        link: "/scratch",
        color: "#4C97FF",
        icon: <SchoolIcon sx={{ fontSize: 30 }} />,
        features: ["Drag & Drop", "Visual Learning", "Kid-friendly"],
    },
    {
        name: "BeE IDE",
        description: "Block-based coding designed specifically for BeE Board development.",
        image: "/beeIco.svg",
        link: "/bee-ide",
        color: "#F1C40F",
        icon: <BuildIcon sx={{ fontSize: 30 }} />,
        features: ["Hardware Control", "Block Coding", "Real-time Feedback"],
    },
    {
        name: "MicroPython",
        description: "Professional text-based development with MicroPython support.",
        image: "/python.webp",
        link: "/python",
        color: "#306998",
        icon: <CodeIcon sx={{ fontSize: 30 }} />,
        features: ["Code Completion", "Syntax Highlighting", "Debug Tools"],
    },
    {
        name: "Jupyter",
        description: "Interactive computing environment for both python and microPython.",
        image: "/jupyter.png",
        link: "/jupyter",
        color: "#F37626",
        icon: <SpeedIcon sx={{ fontSize: 30 }} />,
        features: ["Interactive Cells", "Data Visualization", "Rich Output"],
    },
    {
        name: "IoT",
        description: "Visualize and monitor your IoT projects in real-time.",
        image: "/IoT.png",
        link: "/iot",
        color: "#2ECC71",
        icon: <DashboardCustomizeIcon sx={{ fontSize: 30 }} />,
        features: ["Real-time Data", "Interactive Dashboards", "IoT Integration"],
    },
    {
        name: "Gamepad",
        description: "Control BeE board wirelessly with MQTT.",
        image: "/gamepad.webp",
        link: "/gamepad",
        color: "#b20dff",
        icon: <SportsEsportsIcon sx={{ fontSize: 30 }} />,
        features: ["Real-time Control", "MQTT Integration", "Wireless Communication"],
    },
    {
        name: "Home",
        description: "Back to the home page.",
        image: "/home.png",
        link: "/",
        color: "#F1C40F",
        icon: <ArrowBackIcon sx={{ fontSize: 30 }} />,
        features: ["Continue shopping", "Reading blogs", "Explore projects"],
    },
];

const shine = keyframes`
  0% {
    background-position: -100% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
`;

const float = keyframes`
  0%, 100% { transform: translateY(0) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(-2deg); }
  75% { transform: translateY(10px) rotate(2deg); }
`;

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

function IDE() {
    useDocumentTitle("BeE IDE");
    const navigate = useNavigate();
    const theme = useTheme();

    // Animation variants
    const fadeIn = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: { duration: 0.6 },
        },
    };

    const slideUp = {
        hidden: { y: 50, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: { duration: 0.6 },
        },
    };

    const staggerContainer = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.5,
            },
        },
    };

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.5,
            },
        },
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
        },
    };

    return (
        <>
            <Box
                sx={{
                    background: "linear-gradient(135deg, #1a237e 0%, #0d47a1 100%)",
                    position: "relative",
                    py: 8,
                    "&::before": {
                        content: '""',
                        position: "absolute",
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: "radial-gradient(circle at 50% 50%, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 50%)",
                        pointerEvents: "none",
                    },
                }}
            >
                {/* Animated background elements */}
                <Box
                    sx={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        overflow: "hidden",
                        zIndex: 0,
                        opacity: 0.5,
                        "& > div": {
                            position: "absolute",
                            background: "rgba(255,255,255,0.03)",
                            borderRadius: "50%",
                            animation: `${float} 15s infinite`,
                        },
                    }}
                >
                    {[...Array(6)].map((_, i) => (
                        <Box
                            key={i}
                            sx={{
                                width: Math.random() * 300 + 100,
                                height: Math.random() * 300 + 100,
                                left: `${Math.random() * 100}%`,
                                top: `${Math.random() * 100}%`,
                                animationDelay: `${Math.random() * 2}s`,
                                animationDuration: `${15 + Math.random() * 10}s`,
                            }}
                        />
                    ))}
                </Box>

                <Container maxWidth="lg" sx={{ position: "relative", zIndex: 1 }}>
                    {/* Header Section */}
                    <motion.div initial="hidden" animate="visible" variants={fadeIn}>
                        <Box
                            sx={{
                                textAlign: "center",
                                mb: 8,
                                position: "relative",
                            }}
                        >
                            <Typography
                                component="h1"
                                sx={{
                                    fontSize: { xs: "2.5rem", md: "4rem" },
                                    fontWeight: 800,
                                    background: "linear-gradient(90deg, #ffffff 0%, #88ccff 50%, #ffffff 100%)",
                                    backgroundSize: "200% auto",
                                    color: "transparent",
                                    backgroundClip: "text",
                                    WebkitBackgroundClip: "text",
                                    animation: `${shine} 3s linear infinite`,
                                    // mb: 2,
                                }}
                            >
                                Choose Your App
                            </Typography>
                            <Typography
                                variant="h6"
                                sx={{
                                    color: 'rgba(255, 255, 255, 0.7)',
                                    maxWidth: 700,
                                    mx: 'auto',
                                    mb: 2,
                                    lineHeight: 1.8
                                }}
                            >
                                Developed by BeE STEM Solutions
                            </Typography>
                        </Box>
                    </motion.div>
                    {/* IDE Cards Grid */}
                    <motion.div initial="hidden" animate="visible" variants={staggerContainer}>
                        <Grid container spacing={4}>
                            {ide_list.map((ide, index) => (
                                <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
                                    <motion.div variants={itemVariants}>
                                        <Paper
                                            elevation={24}
                                            sx={{
                                                height: "100%",
                                                background: "rgba(255, 255, 255, 0.03)",
                                                backdropFilter: "blur(10px)",
                                                borderRadius: "20px",
                                                transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                                                cursor: "pointer",
                                                position: "relative",
                                                overflow: "hidden",
                                                border: "1px solid rgba(255, 255, 255, 0.1)",
                                                "&:hover": {
                                                    transform: "translateY(-12px)",
                                                    boxShadow: `0 20px 40px -15px ${ide.color}40`,
                                                    "& .ide-image": {
                                                        transform: "scale(1.1) rotate(5deg)",
                                                    },
                                                    "& .feature-tag": {
                                                        transform: "translateX(0)",
                                                        opacity: 1,
                                                    },
                                                },
                                            }}
                                            onClick={() => navigate(ide.link === "/" ? "/" : `/play${ide.link}`)}
                                        >
                                            {/* Top color bar */}
                                            <Box
                                                sx={{
                                                    height: "4px",
                                                    background: ide.color,
                                                    width: "100%",
                                                }}
                                            />

                                            {/* Content wrapper */}
                                            <Box sx={{ p: 3 }}>
                                                {/* Icon and image */}
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        alignItems: "center",
                                                        justifyContent: "center",
                                                        flexDirection: "column",
                                                        mb: 3,
                                                    }}
                                                >
                                                    <Box
                                                        sx={{
                                                            color: ide.color,
                                                            mb: 2,
                                                            animation: `${pulse} 2s infinite`,
                                                            animationDelay: `${index * 0.2}s`,
                                                        }}
                                                    >
                                                        {ide.icon}
                                                    </Box>
                                                    <Box
                                                        sx={{
                                                            width: 120,
                                                            height: 120,
                                                            display: "flex",
                                                            alignItems: "center",
                                                            justifyContent: "center",
                                                            borderRadius: "50%",
                                                            background: "rgba(255, 255, 255, 0.05)",
                                                            p: 2,
                                                        }}
                                                    >
                                                        <img
                                                            src={ide.image}
                                                            alt={ide.name}
                                                            className="ide-image"
                                                            style={{
                                                                maxWidth: "80%",
                                                                maxHeight: "80%",
                                                                objectFit: "contain",
                                                                transition: "transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)",
                                                            }}
                                                        />
                                                    </Box>
                                                </Box>

                                                {/* Text content */}
                                                <Typography
                                                    variant="h5"
                                                    sx={{
                                                        color: "white",
                                                        fontWeight: 700,
                                                        mb: 1,
                                                        textAlign: "center",
                                                    }}
                                                >
                                                    {ide.name}
                                                </Typography>
                                                <Typography
                                                    variant="body2"
                                                    sx={{
                                                        color: "rgba(255, 255, 255, 0.7)",
                                                        textAlign: "center",
                                                        mb: 3,
                                                        minHeight: 48,
                                                    }}
                                                >
                                                    {ide.description}
                                                </Typography>

                                                {/* Features */}
                                                <Box sx={{ mt: 2 }}>
                                                    {ide.features.map((feature, i) => (
                                                        <Typography
                                                            key={i}
                                                            className="feature-tag"
                                                            sx={{
                                                                color: "rgba(255, 255, 255, 0.9)",
                                                                fontSize: "0.85rem",
                                                                background: `${ide.color}20`,
                                                                p: "4px 12px",
                                                                borderRadius: "12px",
                                                                display: "inline-block",
                                                                m: 0.5,
                                                                transform: "translateX(-20px)",
                                                                opacity: 0,
                                                                transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                                                                transitionDelay: `${i * 0.1}s`,
                                                            }}
                                                        >
                                                            {feature}
                                                        </Typography>
                                                    ))}
                                                </Box>
                                            </Box>
                                        </Paper>
                                    </motion.div>
                                </Grid>
                            ))}
                        </Grid>
                    </motion.div>
                </Container>
            </Box>
        </>
    );
}

export default IDE;
