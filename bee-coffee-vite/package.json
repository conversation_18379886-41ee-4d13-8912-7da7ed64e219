{"name": "bee-coffee-vite", "private": true, "version": "0.1.1", "type": "module", "proxy": "https://api.ipify.org", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@blockly/continuous-toolbox": "^6.0.12", "@blockly/field-colour": "^5.0.12", "@blockly/field-grid-dropdown": "^5.0.12", "@codingame/monaco-vscode-api": "^15.0.3", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/roboto": "^5.0.13", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^5.16.4", "@mui/lab": "^5.0.0-alpha.172", "@mui/material": "^6.1.1", "@mui/x-charts": "^7.27.0", "@mui/x-data-grid": "^7.11.0", "@mui/x-date-pickers": "^7.27.0", "@pyodide/pyodide": "^0.17.1", "@react-oauth/google": "^0.12.1", "@toolpad/core": "^0.7.0", "@uiw/react-codemirror": "^4.23.10", "axios": "^1.7.2", "blockly": "^11.2.1", "ckeditor4": "^4.24.0", "ckeditor4-react": "^5.1.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "esptool-js": "^0.5.4", "framer-motion": "^12.5.0", "jquery": "^3.7.1", "jwt-decode": "^4.0.0", "monaco-editor": "^0.52.2", "mqtt": "^5.13.3", "react": "^18.3.1", "react-date-range": "^2.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-google-recaptcha": "^3.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.25.1", "react-swipeable-views": "^0.14.0", "react-to-print": "^2.15.1", "remark-gfm": "^4.0.1", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "vite": "^5.3.4"}}